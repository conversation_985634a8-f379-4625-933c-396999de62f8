[gd_scene load_steps=21 format=3 uid="uid://bmbgjnvqkamyq"]

[ext_resource type="Script" uid="uid://crtpthkvbit8o" path="res://scripts/Entities/SpaceDebris.cs" id="1_clcas"]
[ext_resource type="Texture2D" uid="uid://cjr324boput30" path="res://Assets/Entities/ShotgunDeath.png" id="3_8bmsm"]
[ext_resource type="Texture2D" uid="uid://ijdya73lxk28" path="res://Assets/Entities/ShotgunDeathAnimation.png" id="3_clcas"]
[ext_resource type="Script" uid="uid://dgshvmje4dtpo" path="res://scripts/Nodes/MovableObject.cs" id="4_yld8b"]

[sub_resource type="CircleShape2D" id="CircleShape2D_txmfh"]
radius = 33.0

[sub_resource type="AtlasTexture" id="AtlasTexture_o0tfo"]
atlas = ExtResource("3_clcas")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8bmsm"]
atlas = ExtResource("3_clcas")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_yld8b"]
atlas = ExtResource("3_clcas")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_qqjge"]
atlas = ExtResource("3_clcas")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_nis8i"]
atlas = ExtResource("3_clcas")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_iycfs"]
atlas = ExtResource("3_clcas")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_fp0xx"]
atlas = ExtResource("3_clcas")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_luhca"]
atlas = ExtResource("3_clcas")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_nkbtg"]
atlas = ExtResource("3_clcas")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_n83rl"]
atlas = ExtResource("3_clcas")
region = Rect2(288, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_uepi3"]
atlas = ExtResource("3_clcas")
region = Rect2(320, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_biods"]
atlas = ExtResource("3_clcas")
region = Rect2(352, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_wumli"]
atlas = ExtResource("3_clcas")
region = Rect2(384, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ucipq"]
atlas = ExtResource("3_clcas")
region = Rect2(416, 0, 32, 32)

[sub_resource type="SpriteFrames" id="SpriteFrames_3y7xr"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_o0tfo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8bmsm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yld8b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qqjge")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nis8i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iycfs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fp0xx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_luhca")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nkbtg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n83rl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uepi3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_biods")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wumli")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ucipq")
}],
"loop": false,
"name": &"default",
"speed": 30.0
}]

[node name="ShotgunDebris" type="RigidBody2D"]
collision_layer = 8
collision_mask = 8
contact_monitor = true
max_contacts_reported = 3
script = ExtResource("1_clcas")
debrisTexture1 = ExtResource("3_8bmsm")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-3, 0)
shape = SubResource("CircleShape2D_txmfh")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
scale = Vector2(2.3, 2.3)
sprite_frames = SubResource("SpriteFrames_3y7xr")
autoplay = "default"

[node name="Sprite2D" type="Sprite2D" parent="."]
visible = false
scale = Vector2(2.3125, 2.3125)

[node name="MovableObject" type="Node2D" parent="."]
script = ExtResource("4_yld8b")
metadata/_custom_type_script = "uid://dgshvmje4dtpo"
