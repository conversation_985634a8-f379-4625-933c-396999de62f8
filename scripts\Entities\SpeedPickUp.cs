using Godot;
using System;

public partial class SpeedPickUp : Area2D
{
    [Export]
    private int MAX_SPEED_INCREMENT = 200;
	p

	public override void _Ready()
	{
		Connect("body_entered", new Callable(this, nameof(OnBodyEntered)));
	}

	private void OnBodyEntered(Node body)
	{
		if (body is Player player)
		{
			// Method 1: Use the player parameter directly (most efficient)
			player.MaxInputSpeed += new Vector2(MAX_SPEED_INCREMENT, MAX_SPEED_INCREMENT);
			GetNode<Sprite2D>("Sprite2D").Visible = false;
			GetNode<Label>("Label").Modulate = new Color(255, 255, 255, 0);
			PlayPickupSound();
		}
	}

    public override void _Process(double delta)
    {
		if( GetNode<Label>("Label").Visible == true)
        {
			GetNode<Label>("Label").hue_rotation += (float)delta;
            QueueFree();
        }
    }


	private void PlayPickupSound()
	{
		var pickupAudio = GetNode<AudioStreamPlayer2D>("AudioPickup");
		if (pickupAudio?.Stream != null)
		{
			// Create a persistent audio player that survives pickup destruction
			AudioStreamPlayer2D persistentAudio = new AudioStreamPlayer2D
			{
				Stream = pickupAudio.Stream,
				GlobalPosition = GlobalPosition,
				VolumeDb = pickupAudio.VolumeDb,
				PitchScale = pickupAudio.PitchScale
			};

			GetParent().AddChild(persistentAudio);
			persistentAudio.Play();
			persistentAudio.Finished += () => persistentAudio.QueueFree();
		}
	}
} 