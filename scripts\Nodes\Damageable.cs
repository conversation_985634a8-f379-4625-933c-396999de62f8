using Godot;
using System;

[GlobalClass]
public partial class Damageable : Node2D
{
	[Export]
	private int maxHealth = 100;
	[Export]
	private bool isDamageable = true;
	[Export]
	private bool isHealable = false;

	private int currentHealth;
	
	[Signal]
	public delegate void HealthChangedEventHandler(int currentHealth, int maxHealth);

	[Signal]
	public delegate void DeathEventHandler();

	[Signal]
	public delegate void MaxHealthChangedEventHandler(int maxHealth, int oldMaxHealth);

	public int MaxHealth => maxHealth;
	public int CurrentHealth => currentHealth;

	public override void _Ready()
	{
		currentHealth = maxHealth;
	}

	public bool Heal(int amount)
	{
		if (!isHealable)
		{
			return false;
		}
		float healAmount = maxHealth * (amount / 100.0f);
		currentHealth += (int)healAmount;
		if (currentHealth > maxHealth)
		{
			currentHealth = maxHealth;
		}
		EmitSignal(SignalName.HealthChanged, currentHealth, maxHealth);

		return true;
	}

	public bool TakeDamage(int amount)
	{
		if (!isDamageable)
		{
			return false;
		}
		currentHealth -= amount;
		EmitSignal(SignalName.HealthChanged, currentHealth, maxHealth);

		if (currentHealth <= 0)
		{
			EmitSignal(SignalName.Death);
			//GetParent().QueueFree();
		}
		return true;
	}

	public void SetHealable(bool isHealable)
	{
		this.isHealable = isHealable;
	}

	public bool GetHealable()
	{
		return isHealable;
	}
	
	public void SetDamageable(bool isDamageable)
	{
		this.isDamageable = isDamageable;
	}

	public bool GetDamageable() {
		return isDamageable;
	}

	public bool IncreaseMaxHealth(int amount){
		int oldMaxHealth = this.maxHealth;
		this.maxHealth += amount;
		this.currentHealth += amount;
		EmitSignal(SignalName.MaxHealthChanged, this.maxHealth, oldMaxHealth);
		EmitSignal(SignalName.HealthChanged, currentHealth, maxHealth);
		return true;
	}
} 
