[gd_scene load_steps=20 format=3 uid="uid://xu0japeolf40"]

[ext_resource type="Script" uid="uid://crtpthkvbit8o" path="res://scripts/Entities/SpaceDebris.cs" id="1_k8x6j"]
[ext_resource type="Texture2D" uid="uid://de2uxo720crt0" path="res://Assets/Entities/Ship_Damaged.png" id="1_txmfh"]
[ext_resource type="Script" uid="uid://dgshvmje4dtpo" path="res://scripts/Nodes/MovableObject.cs" id="3_qhhtn"]
[ext_resource type="Texture2D" uid="uid://b4qulhfqquaa8" path="res://Assets/Entities/V3x_Destruction_Animation_ss.png" id="3_u5np7"]

[sub_resource type="CircleShape2D" id="CircleShape2D_txmfh"]
radius = 33.0

[sub_resource type="AtlasTexture" id="AtlasTexture_03bcq"]
atlas = ExtResource("3_u5np7")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_266db"]
atlas = ExtResource("3_u5np7")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ce6fs"]
atlas = ExtResource("3_u5np7")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_oonxp"]
atlas = ExtResource("3_u5np7")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vqv5m"]
atlas = ExtResource("3_u5np7")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5uboy"]
atlas = ExtResource("3_u5np7")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6lgvj"]
atlas = ExtResource("3_u5np7")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_t67rn"]
atlas = ExtResource("3_u5np7")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_0q7fo"]
atlas = ExtResource("3_u5np7")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_i4j2k"]
atlas = ExtResource("3_u5np7")
region = Rect2(288, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_fifsv"]
atlas = ExtResource("3_u5np7")
region = Rect2(320, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_t38rb"]
atlas = ExtResource("3_u5np7")
region = Rect2(352, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_2j0rt"]
atlas = ExtResource("3_u5np7")
region = Rect2(384, 0, 32, 32)

[sub_resource type="SpriteFrames" id="SpriteFrames_nocii"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_03bcq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_266db")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ce6fs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oonxp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vqv5m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5uboy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6lgvj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t67rn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0q7fo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i4j2k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fifsv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t38rb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2j0rt")
}],
"loop": false,
"name": &"default",
"speed": 30.0
}]

[node name="EnemyDebris" type="RigidBody2D"]
collision_layer = 8
collision_mask = 8
contact_monitor = true
max_contacts_reported = 3
script = ExtResource("1_k8x6j")
debrisTexture1 = ExtResource("1_txmfh")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-3, 0)
shape = SubResource("CircleShape2D_txmfh")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2.3125, 2.3125)

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
scale = Vector2(2.3125, 2.3125)
sprite_frames = SubResource("SpriteFrames_nocii")
autoplay = "default"

[node name="MovableObject" type="Node2D" parent="."]
script = ExtResource("3_qhhtn")
metadata/_custom_type_script = "uid://dgshvmje4dtpo"
